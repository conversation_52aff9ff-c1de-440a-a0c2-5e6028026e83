import React from "react";
import FilterBar, { FilterValue } from "../components/FilterBar";
import MonitorTypePie from "../components/MonitorTypePie";

const AlarmTypeAnalysisPage = () => {
  const [filter, setFilter] = React.useState<
    Pick<FilterValue, "areaId" | "beginDate" | "endDate">
  >({
    areaId: undefined,
    beginDate: null,
    endDate: null,
  });
  return (
    <div className="p-4">
      <FilterBar value={filter} onChange={setFilter} />
      <MonitorTypePie filter={filter} />
    </div>
  );
};

export default AlarmTypeAnalysisPage;
