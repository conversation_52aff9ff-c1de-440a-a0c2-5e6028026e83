import { Popover } from "@douyinfe/semi-ui";
import { useQuery } from "@tanstack/react-query";
import { getTimingStat } from "api/alarm/alarmStat";
import { AlarmDetailCard } from "components/chart/AlarmDetailCard";
import { StatCard } from "components/chart/StatCard";
import { TableChart } from "components/chart/TableChart";
import { TimePeriodCard } from "components/chart/TimePeriodCard";
import LoadingOrError from "components/LoadingOrError";
import React from "react";
import FilterBar, { FilterValue } from "../components/FilterBar";

// 时序统计显示组件
const TimingStatDisplay = ({
  filter,
}: {
  filter: Pick<FilterValue, "areaId" | "beginDate" | "endDate">;
}) => {
  const { data, isLoading } = useQuery({
    queryKey: ["timing-stat", filter],
    queryFn: () =>
      getTimingStat({
        areaId: filter.areaId!,
        beginDate: filter.beginDate!,
        endDate: filter.endDate!,
      }),
    // enabled: !!filter.areaId && !!filter.beginDate && !!filter.endDate,
  });

  // 基础统计卡片配置
  const basicStatCards = [
    {
      label: "总报警数",
      valueField: "totalNum",
      bgColor: "bg-gradient-to-r from-[#44DFDF] to-[#1EA0EA]",
    },
    {
      label: "最大聚集报警数",
      valueField: "maxConcurrentNum",
      bgColor: "bg-gradient-to-r from-[#25AFFE] to-[#AF62C7]",
    },
  ];

  if (isLoading) {
    return <LoadingOrError />;
  }

  const timingData = data?.data as any;
  const peakPeriod = timingData?.peakPeriod;

  return (
    <div className="space-y-6">
      {/* 统计数据展示 - 使用网格布局，充分利用容器宽度 */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-5 items-start">
        {/* 基础统计卡片 - 占据2列空间 */}
        <div className="lg:col-span-2">
          <StatCard
            queryKey={["timing-stat"]}
            queryFn={(params) =>
              getTimingStat({
                areaId: params.areaId!,
                beginDate: params.beginDate!,
                endDate: params.endDate!,
              })
            }
            filter={filter}
            cards={basicStatCards}
            columns={2}
          />
        </div>

        {/* 峰值时间段显示 - 占据1列空间 */}
        <div className="lg:col-span-1">
          {peakPeriod?.beginTime && peakPeriod?.endTime ? (
            <TimePeriodCard
              beginTime={peakPeriod.beginTime}
              endTime={peakPeriod.endTime}
              title="报警峰值时间段"
              bgColor="bg-gradient-to-r from-[#F4C258] to-[#F99336]"
            />
          ) : (
            <div className="flex items-center justify-center bg-gray-100 rounded-lg h-40">
              <span className="text-gray-500">暂无峰值时间段数据</span>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

// 时序详情表格组件
const TimingDetailTable = ({
  filter,
}: {
  filter: Pick<FilterValue, "areaId" | "beginDate" | "endDate">;
}) => {
  // 渲染报警ID列表，每个ID支持Popover详情
  const renderAlarmList = (alarmList: any[]) => {
    if (!alarmList || alarmList.length === 0) {
      return <span className="text-gray-400">--</span>;
    }

    return (
      <div className="flex flex-wrap gap-1">
        {alarmList.map((alarm, index) => (
          <Popover
            key={alarm.id || index}
            content={<AlarmDetailCard alarm={alarm} />}
            trigger="hover"
            position="rightTop"
            showArrow
          >
            <span className="inline-block px-2 py-1 text-xs bg-blue-100 text-blue-600 rounded cursor-pointer hover:bg-blue-200 transition-colors">
              {alarm.id}
            </span>
          </Popover>
        ))}
      </div>
    );
  };

  // 表格列配置
  const columns = [
    {
      title: "序号",
      render: (_: any, __: any, index: number) => index + 1,
      align: "center" as const,
      width: 80,
    },
    {
      title: "开始时间",
      dataIndex: "beginTime",
      render: (value: string) => {
        if (!value) return "--";
        try {
          return new Date(value).toLocaleString("zh-CN");
        } catch {
          return value;
        }
      },
      align: "center" as const,
      width: 180,
    },
    {
      title: "结束时间",
      dataIndex: "endTime",
      render: (value: string) => {
        if (!value) return "--";
        try {
          return new Date(value).toLocaleString("zh-CN");
        } catch {
          return value;
        }
      },
      align: "center" as const,
      width: 180,
    },
    {
      title: "报警列表",
      dataIndex: "alarmList",
      render: renderAlarmList,
      align: "left" as const,
    },
  ];

  return (
    <TableChart
      title="时序报警详情"
      queryKey={["timing-stat-detail"]}
      queryFn={async (params) => {
        const result = await getTimingStat({
          areaId: params.areaId!,
          beginDate: params.beginDate!,
          endDate: params.endDate!,
        });
        // 从 concurrentIntervals 字段提取表格数据
        const timingData = result?.data as any;
        return {
          ...result,
          data: timingData?.concurrentIntervals || [],
        };
      }}
      filter={filter}
      columns={columns}
      height={500}
      emptyText="暂无时序数据"
    />
  );
};

const AlarmTimingAnalysisPage = () => {
  const [filter, setFilter] = React.useState<
    Pick<FilterValue, "areaId" | "beginDate" | "endDate">
  >({
    areaId: undefined,
    beginDate: null,
    endDate: null,
  });

  return (
    <div className="p-4">
      {/*
        方案3：统一容器布局管理 - 一处控制，全局生效

        当前使用：约束宽度居中布局
        - max-w-6xl: 限制最大宽度，适合大屏幕显示
        - mx-auto: 水平居中对齐
        - space-y-6: 统一的垂直间距

        切换到全宽布局：将 "max-w-6xl mx-auto" 改为 "w-full"
        - 方案1（约束宽度）: max-w-6xl mx-auto space-y-6
        - 方案2（全宽布局）: w-full space-y-6

        优势：
        - 所有子组件自动继承相同的布局规则
        - 未来新增组件无需单独设置布局
        - 一行代码即可切换整页布局策略
      */}
      <div className="max-w-6xl mx-auto space-y-6">
        <FilterBar value={filter} onChange={setFilter} />
        <TimingStatDisplay filter={filter} />

        {/* 时序详情表格 */}
        <TimingDetailTable filter={filter} />
      </div>
    </div>
  );
};

export default AlarmTimingAnalysisPage;
