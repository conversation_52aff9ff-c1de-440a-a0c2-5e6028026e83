import { useQuery } from "@tanstack/react-query";
import { getTimingStat } from "api/alarm/alarmStat";
import { StatCard } from "components/chart/StatCard";
import { TimePeriodCard } from "components/chart/TimePeriodCard";
import LoadingOrError from "components/LoadingOrError";
import React from "react";
import FilterBar, { FilterValue } from "../components/FilterBar";

// 时序统计显示组件
const TimingStatDisplay = ({
  filter,
}: {
  filter: Pick<FilterValue, "areaId" | "beginDate" | "endDate">;
}) => {
  const { data, isLoading } = useQuery({
    queryKey: ["timing-stat", filter],
    queryFn: () =>
      getTimingStat({
        areaId: filter.areaId!,
        beginDate: filter.beginDate!,
        endDate: filter.endDate!,
      }),
    // enabled: !!filter.areaId && !!filter.beginDate && !!filter.endDate,
  });

  // 基础统计卡片配置
  const basicStatCards = [
    {
      label: "总报警数",
      valueField: "totalNum",
      bgColor: "bg-gradient-to-r from-[#44DFDF] to-[#1EA0EA]",
    },
    {
      label: "最大聚集报警数",
      valueField: "maxConcurrentNum",
      bgColor: "bg-gradient-to-r from-[#25AFFE] to-[#AF62C7]",
    },
  ];

  if (isLoading) {
    return <LoadingOrError />;
  }

  const timingData = data?.data as any;
  const peakPeriod = timingData?.peakPeriod;

  return (
    <div className="space-y-6">
      {/* 统计数据展示 */}
      <div className="flex justify-center gap-5">
        <StatCard
          queryKey={["timing-stat"]}
          queryFn={(params) =>
            getTimingStat({
              areaId: params.areaId!,
              beginDate: params.beginDate!,
              endDate: params.endDate!,
            })
          }
          filter={filter}
          cards={basicStatCards}
          columns={2}
        />

        {/* 峰值时间段显示 */}
        {peakPeriod?.beginTime && peakPeriod?.endTime ? (
          <TimePeriodCard
            beginTime={peakPeriod.beginTime}
            endTime={peakPeriod.endTime}
            title="报警峰值时间段"
            bgColor="bg-gradient-to-r from-[#F4C258] to-[#F99336]"
          />
        ) : (
          <div className="flex items-center justify-center bg-gray-100 rounded-lg h-40">
            <span className="text-gray-500">暂无峰值时间段数据</span>
          </div>
        )}
      </div>
    </div>
  );
};

const AlarmTimingAnalysisPage = () => {
  const [filter, setFilter] = React.useState<
    Pick<FilterValue, "areaId" | "beginDate" | "endDate">
  >({
    areaId: undefined,
    beginDate: null,
    endDate: null,
  });

  return (
    <div className="p-4 space-y-6">
      <FilterBar value={filter} onChange={setFilter} />

      {/* 统计数据展示 */}
      <TimingStatDisplay filter={filter} />
    </div>
  );
};

export default AlarmTimingAnalysisPage;
