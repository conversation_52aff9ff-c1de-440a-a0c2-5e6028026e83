import React from "react";
import AlarmNumStatsTable from "../components/AlarmNumStatsTable";
import FilterBar, { FilterValue } from "../components/FilterBar";

const AlarmCountAnalysisPage = () => {
  const [filter, setFilter] = React.useState<FilterValue>({
    areaId: undefined,
    beginDate: null,
    endDate: null,
    topN: 10,
  });

  // 检查是否有有效的筛选条件来显示表格
  const hasValidFilter =
    filter.areaId != null && filter.beginDate != null && filter.endDate != null;

  return (
    <div className="p-4">
      <FilterBar value={filter} onChange={setFilter} showTopN />

      <AlarmNumStatsTable
        filter={filter}
      />
    </div>
  );
};

export default AlarmCountAnalysisPage;
