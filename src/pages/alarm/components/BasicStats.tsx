import { getAlarmDashStat } from "api/alarm/alarmStat";
import { StatCard } from "components/chart/StatCard";

export default function BasicStats() {
  return (
    <div className="mb-6">
      <StatCard
        queryKey={["getAlarmDashStat"]}
        queryFn={getAlarmDashStat}
        cards={[
          { label: "监测指标数", valueField: "sensorNum" },
          { label: "运行中监测指标", valueField: "sensorActiveNum" },
          { label: "指标类型", valueField: "monitorTypeNum" },
          { label: "报警原因", valueField: "alarmReasonNum" },
          { label: "报警措施", valueField: "alarmMeasureNum" },
        ]}
        columns={5}
        height={160}
        gap={20}
      />
    </div>
  );
}
