import { getAlarmTrendStat } from "api/alarm/alarmStat";
import {
  TrendChart,
  buildSmoothAreaLineOption,
} from "components/chart/TrendChart";
import { formatDate } from "utils";
import { FilterValue } from "./FilterBar";

interface AlarmTrendChartProps {
  filter: Pick<FilterValue, "areaId" | "beginDate" | "endDate">;
}

export default function AlarmTrendChart({ filter }: AlarmTrendChartProps) {
  const safeFormatDate = (v: any) => formatDate(v) || "";
  return (
    <TrendChart
      title="报警数量趋势"
      queryKey={["getAlarmTrendStat"]}
      queryFn={getAlarmTrendStat}
      filter={filter}
      optionBuilder={buildSmoothAreaLineOption({
        xField: "statTime",
        yField: "num",
        xFormatter: safeFormatDate,
        lineColor: "#60B7FF",
        areaColor: "#60B7FF",
        areaTo: "#fff",
      })}
      height={300}
    />
  );
}
