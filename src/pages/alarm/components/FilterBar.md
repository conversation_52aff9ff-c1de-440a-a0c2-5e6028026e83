# FilterBar 组件文档

## 概述

FilterBar 是报警首页的筛选组件，支持区域、时间范围和 TopN 筛选功能。该组件提供了统一的类型定义，供整个报警模块使用。

## 类型定义

### 核心类型

```typescript
// 筛选状态类型（允许null/undefined）
export interface FilterValue {
  areaId: number | null | undefined;
  beginDate: string | null;
  endDate: string | null;
  topN?: number;
}

// 有效筛选类型（要求非空）
export interface ValidFilter {
  areaId: number;
  beginDate: string;
  endDate: string;
  topN?: number;
}

// 筛选区props类型
export interface FilterBarProps {
  value: FilterValue;
  onChange: (v: FilterValue) => void;
  showTopN?: boolean; // 控制是否显示topN筛选项
}
```

### 类型使用场景

- **FilterValue**: 用于筛选状态管理，允许空值，适用于用户输入状态
- **ValidFilter**: 用于 API 调用，要求非空值，确保数据完整性
- **Pick<FilterValue, 'areaId' | 'beginDate' | 'endDate'>**: 用于不需要 topN 的图表组件

### 参数说明

- `value`: 当前筛选值对象（FilterValue 类型）
  - `areaId`: 选中的区域ID，可为 null 或 undefined
  - `beginDate`: 开始日期，RFC3339格式字符串，可为 null
  - `endDate`: 结束日期，RFC3339格式字符串，可为 null
  - `topN`: 显示前N条记录，可选，默认为10，最小值为1
- `onChange`: 筛选值变化时的回调函数
- `showTopN`: 是否显示TopN筛选项，默认为false

## 使用示例

### 基础用法（图表组件）

```typescript
import React, { useState } from 'react';
import FilterBar, { FilterValue } from './components/FilterBar';
import MonitorTypePie from './components/MonitorTypePie';

const AlarmTypeAnalysisPage = () => {
  const [filter, setFilter] = useState<Pick<FilterValue, 'areaId' | 'beginDate' | 'endDate'>>({
    areaId: undefined,
    beginDate: null,
    endDate: null,
  });

  return (
    <div className="p-4">
      <FilterBar value={filter} onChange={setFilter} />
      <MonitorTypePie filter={filter} />
    </div>
  );
};
```

### 带TopN筛选的用法（表格组件）

```typescript
import React, { useState } from 'react';
import FilterBar, { FilterValue } from './components/FilterBar';
import AlarmNumStatsTable from './components/AlarmNumStatsTable';

const AlarmCountAnalysisPage = () => {
  const [filter, setFilter] = useState<FilterValue>({
    areaId: undefined,
    beginDate: null,
    endDate: null,
    topN: 10,
  });

  // 检查是否有有效的筛选条件来显示表格
  const hasValidFilter =
    filter.areaId != null &&
    filter.beginDate != null &&
    filter.endDate != null;

  return (
    <div className="p-4">
      <FilterBar value={filter} onChange={setFilter} showTopN />
      {hasValidFilter && (
        <AlarmNumStatsTable
          filter={{
            areaId: filter.areaId!,
            beginDate: filter.beginDate!,
            endDate: filter.endDate!,
            topN: filter.topN,
          }}
        />
      )}
    </div>
  );
};
```

### 完整示例（报警首页）

```typescript
import React, { useState } from 'react';
import FilterBar, { FilterValue } from './components/FilterBar';
import AlarmTrendChart from './components/AlarmTrendChart';
import MonitorTypePie from './components/MonitorTypePie';
import AlarmPriorityPie from './components/AlarmPriorityPie';
import AlarmNumStatsTable from './components/AlarmNumStatsTable';
import AlarmDurationStatsTable from './components/AlarmDurationStatsTable';

export function AlarmIndexContent() {
  // 统一管理筛选条件
  const [filter, setFilter] = useState<FilterValue>({
    areaId: null,
    beginDate: null,
    endDate: null,
  });

  // 检查是否有有效的筛选条件来显示表格
  const hasValidFilter =
    filter.areaId != null && filter.beginDate != null && filter.endDate != null;

  return (
    <div className="page-root">
      <FilterBar value={filter} onChange={setFilter} />

      {/* 趋势图 */}
      <AlarmTrendChart filter={filter} />

      {/* 饼图 */}
      <div className="grid grid-cols-2 gap-x-5">
        <MonitorTypePie filter={filter} />
        <AlarmPriorityPie filter={filter} />
      </div>

      {/* 统计表格 - 条件渲染 */}
      {hasValidFilter && (
        <div className="grid grid-cols-2 gap-x-5">
          <AlarmNumStatsTable
            filter={{
              areaId: filter.areaId!,
              beginDate: filter.beginDate!,
              endDate: filter.endDate!,
              topN: filter.topN,
            }}
          />
          <AlarmDurationStatsTable
            filter={{
              areaId: filter.areaId!,
              beginDate: filter.beginDate!,
              endDate: filter.endDate!,
              topN: filter.topN,
            }}
          />
        </div>
      )}
    </div>
  );
}
```

## 特性

### 类型系统特性

1. **分层类型设计**: 提供 FilterValue 和 ValidFilter 两种类型
2. **类型安全**: 确保编译时类型检查和运行时数据完整性
3. **工具类型支持**: 支持 Pick、Omit 等 TypeScript 工具类型
4. **统一导出**: 所有相关组件使用统一的类型定义

### TopN 筛选特性

1. **默认值**: TopN 默认为 10
2. **最小值限制**: 输入值必须大于 0，自动校正为最小值 1
3. **最大值限制**: 最大值为 1000
4. **条件显示**: 只有当 `showTopN` 为 true 时才显示
5. **自动校正**: 无效输入会自动校正为默认值 10

### 数据验证

- TopN 值会通过 `Math.max(1, Number(v) || 10)` 进行校验
- 确保值始终为正整数
- 无效输入时回退到默认值

### 样式特性

- 使用 Tailwind CSS 进行样式设计
- 响应式布局，适配不同屏幕尺寸
- 与现有筛选项保持一致的视觉风格

## 最佳实践

### 条件渲染模式

对于需要 ValidFilter 的组件（如表格），建议使用条件渲染：

```typescript
const hasValidFilter =
  filter.areaId != null &&
  filter.beginDate != null &&
  filter.endDate != null;

return (
  <div>
    <FilterBar value={filter} onChange={setFilter} />
    {hasValidFilter && (
      <DataTable filter={{
        areaId: filter.areaId!,
        beginDate: filter.beginDate!,
        endDate: filter.endDate!,
        topN: filter.topN,
      }} />
    )}
  </div>
);
```

### 类型导入

```typescript
// 导入类型
import FilterBar, { FilterValue, ValidFilter } from './components/FilterBar';

// 使用场景
const [filter, setFilter] = useState<FilterValue>({...});
const [validFilter, setValidFilter] = useState<ValidFilter>({...});
```

## 注意事项

1. **类型选择**: 根据使用场景选择合适的类型

   - 状态管理使用 `FilterValue`
   - API 调用使用 `ValidFilter`
   - 图表组件使用 `Pick<FilterValue, ...>`

2. **条件渲染**: 对于需要完整数据的组件，使用条件渲染确保数据完整性

3. **非空断言**: 在已验证的情况下使用非空断言操作符 (`!`)

4. **TopN 筛选**: 默认不显示，需要通过 `showTopN` 参数控制

5. **组件依赖**: 使用 Semi UI 组件库，确保项目已正确安装依赖

## 相关组件

### 使用 FilterValue 类型的组件

- `AlarmIndexContent` - 报警首页主组件
- `AlarmCountAnalysisPage` - 报警次数分析页面
- `AlarmDurationAnalysisPage` - 报警时长分析页面

### 使用 ValidFilter 类型的组件

- `AlarmNumStatsTable` - 报警次数统计表格
- `AlarmDurationStatsTable` - 报警时长统计表格

### 使用 Pick<FilterValue> 类型的组件

- `MonitorTypePie` - 监测类型分布饼图
- `AlarmPriorityPie` - 报警优先级分布饼图
- `AlarmTrendChart` - 报警趋势图
- `AlarmTypeAnalysisPage` - 报警类型分析页面
- `AlarmPriorityAnalysisPage` - 报警优先级分析页面

## 文件引用

- 组件源码: [src/pages/alarm/components/FilterBar.tsx]
- 类型重构日志: [docs/dev-log/20250626-AlarmFilterType.md]
- 使用示例: [src/pages/alarm/alarmIndexContent.tsx]
- 开发规范: [.augment-guidelines]
