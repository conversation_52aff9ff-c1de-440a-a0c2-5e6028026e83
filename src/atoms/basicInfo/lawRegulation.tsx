import { Tag, Tooltip } from "@douyinfe/semi-ui";
import {
  LAW_REGULATION_COERCIONLEVEL_MAP,
  LAW_REGULATION_TYPE_MAP,
} from "components";
import dayjs from "dayjs";
import { atom } from "jotai";
import { atomWithReset } from "jotai/utils";
import { find, propEq } from "ramda";
import { CommonAtoms } from "types";
import { formatDateDay, millisecondsOfOnemonth } from "utils";

export const lawRegulationFilterAtom = atomWithReset({
  pageNumber: 1,
  pageSize: 10,
});

//TODO: to delete
export const lawRegulationFnAtom = atom({
  refetch: () => {},
});

export const lawRegulationEditModalAtom = atomWithReset({
  id: "",
  show: false,
});

export const lawRegulationConfigModalAtom = atom(false);

export const lawRegulationColumnsAtom = atom([
  {
    title: "ID",
    dataIndex: "id",
    isShow: true,
    width: 80,
  },
  // user-defined code here
  {
    title: "法律法规名称",
    dataIndex: "name",
    isShow: true,
    ellipsis: true,
  },
  {
    title: "法规编号",
    dataIndex: "code",
    isShow: true,
    ellipsis: true,
  },
  {
    title: "分类",
    dataIndex: "type",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => {
      const i = find(propEq(item, "id"))(LAW_REGULATION_TYPE_MAP);
      return (
        <Tooltip content={i.name}>
          <Tag color={i.color} type="light">
            {i.name}
          </Tag>
        </Tooltip>
      );
    },
    renderText: (item) => {
      const i = find(propEq(item, "id"))(LAW_REGULATION_TYPE_MAP);
      return i.name;
    },
  },
  {
    title: "强制程度",
    dataIndex: "coercionLevel",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => {
      const i = find(propEq(item, "id"))(LAW_REGULATION_COERCIONLEVEL_MAP);
      return (
        <Tooltip content={i.name}>
          <Tag color={i.color} type="light">
            {i.name}
          </Tag>
        </Tooltip>
      );
    },
    renderText: (item) => {
      const i = find(propEq(item, "id"))(LAW_REGULATION_COERCIONLEVEL_MAP);
      return i.name;
    },
  },
  {
    title: "发布机构",
    dataIndex: "issueAuthority",
    isShow: true,
    ellipsis: true,
  },
  {
    title: "启用日期",
    dataIndex: "validDate",
    isShow: true,
    ellipsis: true,
    render: (item) => {
      const content = formatDateDay(item);
      if (!content) {
        return null;
      }
      return (
        <Tooltip content={content}>
          <p>{content}</p>
        </Tooltip>
      );
    },
    renderText: (item) => {
      return formatDateDay(item);
    },
  },
  {
    title: "废止日期",
    dataIndex: "invalidDate",
    isShow: true,
    ellipsis: true,
    render: (text) => {
      if (!text) {
        return null;
      }
      const date = dayjs(text);
      const diff = date.diff(dayjs());
      const dateShow = date.format("YYYY-MM-DD");

      return (
        <Tooltip content={dateShow}>
          <p>
            {diff < millisecondsOfOnemonth ? (
              <Tag color="red">{dateShow}</Tag>
            ) : diff < 3 * millisecondsOfOnemonth ? (
              <Tag color="yellow">{dateShow}</Tag>
            ) : (
              <Tag color="white">{dateShow}</Tag>
            )}
          </p>
        </Tooltip>
      );
    },
    renderText: (text) => {
      return formatDateDay(text);
    },
  },
]);

export const lawRegulationAtoms: CommonAtoms = {
  entity: "LawRegulation",
  entityCName: "法律法规信息",
  filter: lawRegulationFilterAtom,
  Fn: lawRegulationFnAtom,
  editModal: lawRegulationEditModalAtom,
  configModal: lawRegulationConfigModalAtom,
  columns: lawRegulationColumnsAtom,
};
