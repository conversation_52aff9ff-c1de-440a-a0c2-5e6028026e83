import { Tag, Tooltip } from "@douyinfe/semi-ui";
import { MAJOR_HAZARD_LEVEL_MAP } from "components";
import { Atom, atom } from "jotai";
import { atomWithReset } from "jotai/utils";
import { find, propEq } from "ramda";
import { CommonAtoms } from "types";

export const majorHazardFilterAtom = atomWithReset({
  pageNumber: 1,
  pageSize: 10,
});

//TODO: to delete
export const majorHazardFnAtom = atom({
  refetch: () => {},
});

export const majorHazardEditModalAtom = atomWithReset({
  id: "",
  show: false,
});

export const majorHazardConfigModalAtom = atom(false);

export const majorHazardColumnsAtom = atom([
  {
    title: "ID",
    dataIndex: "id",
    isShow: true,
    width: 80,
  },
  {
    title: "重大危险源名称",
    dataIndex: "name",
    isShow: true,
    ellipsis: true,
  },
  {
    title: "危险源编码",
    dataIndex: "code",
    isShow: true,
  },
  {
    title: "危险源简称",
    dataIndex: "shortName",
    isShow: true,
  },
  {
    title: "R值",
    dataIndex: "R",
    isShow: true,
  },
  {
    title: "危险源等级",
    dataIndex: "level",
    isShow: true,
    render: (item) => {
      const i = find(propEq(item, "id"))(MAJOR_HAZARD_LEVEL_MAP);
      return (
        <Tooltip content={i.name}>
          <Tag color={i.color} type="light">
            {i.name}
          </Tag>
        </Tooltip>
      );
    },
  },
  {
    title: "摄像头",
    dataIndex: "monitors",
    isShow: true,
    render: (item) =>
      item?.map((o) => {
        return <a href={o.videoPath}>{o.name}</a>;
      }),
  },
]);

export const majorHazardEvaluateChemicalColumnsAtom = atom([
  {
    title: "名称",
    dataIndex: "name",
    isShow: true,
    ellipsis: true,
  },
  {
    title: "校正系数β",
    dataIndex: "beta",
    isShow: true,
    ellipsis: true,
  },
  {
    title: "实际存在量q(吨)",
    dataIndex: "realValue",
    isShow: true,
    ellipsis: true,
  },
  {
    title: "临界量Q(吨)",
    dataIndex: "thresholdValue",
    isShow: true,
    ellipsis: true,
  },
]);

export const majorHazardEvaluateModalAtom = atomWithReset({
  id: "",
  show: false,
});

export const majorHazardEvaluateModalChemicalModalAtom = atomWithReset({
  chemical: {},
  show: false,
});

export type MajorHazardAtoms = CommonAtoms & {
  evaluateModal: Atom<any>;
  evaluateChemicalColumns: Atom<any>;
  evaluateChemicalModal: Atom<any>;
};

export const majorHazardAtoms: MajorHazardAtoms = {
  entity: "MajorHazard",
  filter: majorHazardFilterAtom,
  Fn: majorHazardFnAtom,
  editModal: majorHazardEditModalAtom,
  configModal: majorHazardConfigModalAtom,
  columns: majorHazardColumnsAtom,
  evaluateModal: majorHazardEvaluateModalAtom,
  evaluateChemicalColumns: majorHazardEvaluateChemicalColumnsAtom,
  evaluateChemicalModal: majorHazardEvaluateModalChemicalModalAtom,
};
