import { Tag, Tooltip } from "@douyinfe/semi-ui";
import {
  CHEMICAL_STORAGETYPE_MAP,
  CHEMICAL_TYPE_MAP,
  CHEMICAL_USAGE_MAP,
  IS_ISNOT_MAP,
} from "components";
import { atom } from "jotai";
import { atomWithReset } from "jotai/utils";
import { find, propEq } from "ramda";
import { CommonAtoms } from "types";

export const chemicalFilterAtom = atomWithReset({
  pageNumber: 1,
  pageSize: 10,
});

//TODO: to delete
export const chemicalFnAtom = atom({
  refetch: () => {},
});

export const chemicalEditModalAtom = atomWithReset({
  id: "",
  show: false,
});

export const chemicalConfigModalAtom = atom(false);

export const chemicalColumnsAtom = atom([
  {
    title: "ID",
    dataIndex: "id",
    isShow: true,
    width: 80,
  },
  // user-defined code here
  {
    title: "化学品中文名",
    dataIndex: "name",
    isShow: true,
    ellipsis: true,
  },
  {
    title: "化学品编号",
    dataIndex: "code",
    isShow: true,
    ellipsis: true,
  },
  {
    title: "化学品类型",
    dataIndex: "type",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => {
      const i = find(propEq(item, "id"))(CHEMICAL_TYPE_MAP);
      return (
        <Tooltip content={i.name}>
          <Tag color={i.color} type="light">
            {i.name}
          </Tag>
        </Tooltip>
      );
    },
  },
  {
    title: "是否重点监管危化品",
    dataIndex: "isKeyRegulatory",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => {
      const i = find(propEq(item ? item : 1, "id"))(IS_ISNOT_MAP);
      return (
        <Tooltip content={i.name}>
          <Tag color={i.color} type="light">
            {i.name}
          </Tag>
        </Tooltip>
      );
    },
  },
  {
    title: "存储方式",
    dataIndex: "storageType",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => {
      const i = find(propEq(item, "id"))(CHEMICAL_STORAGETYPE_MAP);
      return (
        <Tooltip content={i.name}>
          <Tag color={i.color} type="light">
            {i.name}
          </Tag>
        </Tooltip>
      );
    },
  },
  {
    title: "作用",
    dataIndex: "usage",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => {
      const i = find(propEq(item, "id"))(CHEMICAL_USAGE_MAP);
      return (
        <Tooltip content={i.name}>
          <Tag color={i.color} type="light">
            {i.name}
          </Tag>
        </Tooltip>
      );
    },
  },
]);

export const chemicalAtoms: CommonAtoms = {
  entity: "Chemical",
  filter: chemicalFilterAtom,
  Fn: chemicalFnAtom,
  editModal: chemicalEditModalAtom,
  configModal: chemicalConfigModalAtom,
  columns: chemicalColumnsAtom,
};
