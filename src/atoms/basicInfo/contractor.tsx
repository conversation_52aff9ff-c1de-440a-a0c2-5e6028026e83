import { Tag, Tooltip } from "@douyinfe/semi-ui";
import {
  BIM_CONTRACTOR_ISBLACK_MAP,
  BIM_CONTRACTOR_TYPE_MAP,
} from "components";
import { atom } from "jotai";
import { atomWithReset } from "jotai/utils";
import { find, propEq } from "ramda";
import { CommonAtoms } from "types";

export const contractorFilterAtom = atomWithReset({
  pageNumber: 1,
  pageSize: 10,
});

//TODO: to delete
export const contractorFnAtom = atom({
  refetch: () => {},
});

export const contractorDetailSideAtom = atomWithReset({
  id: "",
  show: false,
});

export const contractorEditModalAtom = atomWithReset({
  id: "",
  show: false,
});

export const contractorConfigModalAtom = atom(false);

export const contractorColumnsAtom = atom([
  {
    title: "ID",
    dataIndex: "id",
    isShow: true,
    width: 80,
  },
  {
    title: "承包商类型",
    dataIndex: "contractorType",
    isShow: true,
    width: 120,
    render: (item) => {
      const i = find(propEq(item ? item : 1, "id"))(BIM_CONTRACTOR_TYPE_MAP);
      return <p>{i?.name ?? ""}</p>;
    },
  },
  {
    title: "承包商名称",
    dataIndex: "name",
    isShow: true,
    // TODO
    //sorter: (a, b) => (a.name.length - b.name.length > 0 ? 1 : -1),
  },
  {
    title: "联络人",
    dataIndex: "firstContractor",
    isShow: true,
    ellipsis: true,
  },
  {
    title: "联络人电话",
    dataIndex: "firstMobile",
    isShow: true,
  },
  {
    title: "是否黑名单",
    dataIndex: "isBlack",
    isShow: true,
    render: (item) => {
      const i = find(propEq(item, "id"))(BIM_CONTRACTOR_ISBLACK_MAP);
      return (
        <Tooltip content={i.name}>
          <Tag color={i.color} type="light">
            {i.name}
          </Tag>
        </Tooltip>
      );
    },
  },
  {
    title: "拉黑原因",
    dataIndex: "blackReason",
    isShow: false,
    ellipsis: true,
  },

  // TODO: 是否更新怎么做
  /* {
    title: "是否有更新",
  }, */
]);

export const contractorShareModalAtom = atomWithReset({
  contractor: {},
  show: false,
});

export const contractorBlackModalAtom = atomWithReset({
  contractor: {},
  show: false,
});

export const contractorAtoms: CommonAtoms = {
  entity: "Contractor",
  filter: contractorFilterAtom,
  Fn: contractorFnAtom,
  editModal: contractorEditModalAtom,
  configModal: contractorConfigModalAtom,
  columns: contractorColumnsAtom,
};
