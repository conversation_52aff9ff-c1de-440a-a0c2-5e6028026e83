import { Tag, Tooltip } from "@douyinfe/semi-ui";
import { BbCheckParams } from "api";
import {
  CHECK_CYCLE_UNIT_MAP,
  CLASSIFY1_MAP,
  CLASSIFY2_MAP,
  DANGER_REPORT_STATUS_MAP,
} from "components";
import { atom } from "jotai";
import { atomWithReset } from "jotai/utils";
import { find, propEq, whereEq } from "ramda";

export const bbCheckConfigModalAtom = atom(false);

export const bbCheckEditModalAtom = atom({
  id: "",
  show: false,
});

// 查询条件
export const bbCheckFilterAtom = atomWithReset<BbCheckParams>({
  pageNumber: 1,
  pageSize: 10,
  query: "",
  filter: {},
});

export const bbCheckSelectAtom = atomWithReset<string | null>(null);
// 查询条件
export const bbCheckFnAtom = atom({
  refetch: () => {},
});

export const bbCheckColumnsAtom = atom([
  {
    title: <Tooltip content="ID">ID</Tooltip>,
    dataIndex: "id",
    isShow: true,
    width: 80,
  },
  {
    title: <Tooltip content="风险对象">风险对象</Tooltip>,
    dataIndex: "riskObject",
    isShow: true,
    ellipsis: true,
    render: (item) => <p>{item?.name}</p>,
  },
  {
    title: <Tooltip content="风险单元">风险单元</Tooltip>,
    dataIndex: "riskUnit",
    isShow: true,
    ellipsis: true,
    render: (item) => <p>{item?.name}</p>,
  },
  {
    title: <Tooltip content="风险事件">风险事件</Tooltip>,
    dataIndex: "riskEvent",
    isShow: true,
    ellipsis: true,
    render: (item) => <p>{item?.name}</p>,
  },
  {
    title: <Tooltip content="一级分类">一级分类</Tooltip>,
    dataIndex: "classify1",
    isShow: true,
    ellipsis: true,
    render: (item) => {
      const i = find(propEq(item, "id"))(CLASSIFY1_MAP);
      return (
        <Tag color="grey" type="light" className="min-w-[fit-content]">
          {i.name}
        </Tag>
      );
    },
  },
  {
    title: <Tooltip content="二级分类">二级分类</Tooltip>,
    dataIndex: "classify2",
    isShow: true,
    ellipsis: true,
    render: (item) => {
      const k = item?.split("-");
      const i = find(
        whereEq({
          pid: parseInt(k[0]),
          id: parseInt(k[1]),
        })
      )(CLASSIFY2_MAP);

      return (
        <Tag color="grey" type="light" className="min-w-[fit-content]">
          {i.name}
        </Tag>
      );
    },
  },
  {
    title: <Tooltip content="三级分类">三级分类</Tooltip>,
    dataIndex: "classify3",
    isShow: true,
    ellipsis: true,
    render: (text) => (
      <Tooltip content={text}>
        <span className="block truncate">{text}</span>
      </Tooltip>
    ),
  },
  {
    title: <Tooltip content="包保责任">包保责任</Tooltip>,
    dataIndex: "controlMeasure",
    isShow: true,
    ellipsis: true,
    render: (text) => (
      <Tooltip content={text}>
        <span className="block truncate">{text}</span>
      </Tooltip>
    ),
  },
  {
    title: <Tooltip content="隐患排查内容">隐患排查内容</Tooltip>,
    dataIndex: "troubleShootContent",
    isShow: true,
    ellipsis: true,
    render: (text) => (
      <Tooltip content={text}>
        <span className="block truncate">{text}</span>
      </Tooltip>
    ),
  },
  {
    // title: <Tooltip content="排查周期">排查周期</Tooltip>,
    dataIndex: "checkCycle",
    // isShow: true,
    // ellipsis: true,
    align: "center",
    // }, {
    title: <Tooltip content="任务排查周期">任务排查周期</Tooltip>,
    // dataIndex: 'checkCycleUnit',
    isShow: true,
    ellipsis: true,
    render: (text, record) => {
      const i = find(
        propEq(record?.checkCycleUnit ? record?.checkCycleUnit : 1, "id")
      )(CHECK_CYCLE_UNIT_MAP);
      if (record.checkCycle === undefined) return null;
      return (
        <Tag color="grey" type="light" className="min-w-[fit-content]">
          {record.checkCycle}
          {i.id !== 2 ? "个" : ""}
          {i.name}
        </Tag>
      );
    },
  },
  {
    title: <Tooltip content="持续时间">持续时间</Tooltip>,
    dataIndex: "duration",
    align: "center",
    isShow: true,
    ellipsis: true,
    render: (text, record) => {
      const i = find(
        propEq(record?.durationUnit ? record?.durationUnit : 1, "id")
      )(CHECK_CYCLE_UNIT_MAP);
      if (record.duration === undefined) return null;
      return (
        <Tag color="grey" type="light" className="min-w-[fit-content]">
          {record.duration}
          {i.id !== 2 ? "个" : ""}
          {i.name}
        </Tag>
      );
    },
  },
  {
    title: <Tooltip content="上报状态">上报状态</Tooltip>,
    dataIndex: "uploadStatus",
    isShow: false,
    ellipsis: true,
    render: (item) => {
      const i = find(propEq(item, "id"))(DANGER_REPORT_STATUS_MAP);
      return (
        <Tooltip content={i?.name ?? "-"}>
          <Tag color={i?.color} type="light">
            {i?.name ?? "-"}
          </Tag>
        </Tooltip>
      );
    },
  },
]);
