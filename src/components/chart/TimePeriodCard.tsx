import { IconCalendar, IconClock } from "@douyinfe/semi-icons";

export interface TimePeriodCardProps {
  beginTime: string;
  endTime: string;
  title?: string;
  bgColor?: string;
  height?: number;
  className?: string;
  onClick?: () => void;
}

/**
 * 时间段显示卡片组件
 * 专门用于显示起止时间段，美观直观
 */
export function TimePeriodCard({
  beginTime,
  endTime,
  title = "峰值时间段",
  bgColor = "bg-gradient-to-r from-[#667eea] to-[#764ba2]",
  height = 160,
  className = "",
  onClick,
}: TimePeriodCardProps) {
  // 格式化时间显示
  const formatTime = (timeStr: string) => {
    if (!timeStr) return "--:--";
    try {
      const date = new Date(timeStr);
      return date.toLocaleTimeString("zh-CN", {
        hour: "2-digit",
        minute: "2-digit",
        hour12: false,
      });
    } catch {
      return timeStr;
    }
  };

  // 格式化日期显示
  const formatDate = (timeStr: string) => {
    if (!timeStr) return "--";
    try {
      const date = new Date(timeStr);
      return date.toLocaleDateString("zh-CN", {
        month: "2-digit",
        day: "2-digit",
      });
    } catch {
      return timeStr;
    }
  };

  // 计算时间段长度
  const calculateDuration = () => {
    if (!beginTime || !endTime) return "";
    try {
      const start = new Date(beginTime);
      const end = new Date(endTime);
      const diffMs = end.getTime() - start.getTime();
      const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
      const diffMinutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));

      if (diffHours > 0) {
        return `${diffHours}小时${diffMinutes}分钟`;
      }
      return `${diffMinutes}分钟`;
    } catch {
      return "";
    }
  };

  const duration = calculateDuration();

  return (
    <div
      className={`flex flex-col justify-between overflow-hidden rounded-lg ${bgColor} p-6 relative group ${onClick ? "cursor-pointer" : ""} ${className}`}
      style={{ height: `${height}px` }}
      onClick={onClick}
    >
      {/* 标题 */}
      <div className="flex items-center gap-2 text-white/90 text-sm">
        <IconClock size="small" />
        <span>{title}</span>
      </div>

      {/* 主要时间显示区域 */}
      <div className="flex-1 flex flex-col justify-center text-white">
        {/* 时间段显示 */}
        <div className="space-y-2">
          {/* 开始时间 */}
          <div className="flex items-center gap-3">
            <div className="text-2xl font-bold">{formatTime(beginTime)}</div>
            <div className="text-sm opacity-80">{formatDate(beginTime)}</div>
          </div>

          {/* 分隔线和箭头 */}
          <div className="flex items-center gap-2 my-1">
            <div className="flex-1 h-px bg-white/30"></div>
            <div className="text-white/60 text-xs">至</div>
            <div className="flex-1 h-px bg-white/30"></div>
          </div>

          {/* 结束时间 */}
          <div className="flex items-center gap-3">
            <div className="text-2xl font-bold">{formatTime(endTime)}</div>
            <div className="text-sm opacity-80">{formatDate(endTime)}</div>
          </div>
        </div>
      </div>

      {/* 底部持续时长 */}
      {duration && (
        <div className="flex items-center gap-2 text-white/80 text-sm">
          <IconCalendar size="small" />
          <span>持续 {duration}</span>
        </div>
      )}

      {/* 悬浮效果 */}
      <div className="absolute inset-0 bg-white/10 opacity-0 group-hover:opacity-100 transition-opacity duration-200 rounded-lg"></div>
    </div>
  );
}
